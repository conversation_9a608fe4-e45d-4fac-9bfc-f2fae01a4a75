#include "HgbParser.h"
#include "utils/Logger.h"
#include <QFile>
#include <QDir>
#include <QProcess>
#include <QTemporaryDir>
#include <QStandardPaths>
#include <cstring>
#include <algorithm>
#include <cmath>
#include <sstream>
#include <iomanip>
#include <vector>

// 字节序转换辅助函数
namespace {
    uint16_t readUint16(const uint8_t* ptr, bool isBigEndian) {
        uint16_t value = *reinterpret_cast<const uint16_t*>(ptr);
        return isBigEndian ? ((value & 0xFF) << 8) | ((value >> 8) & 0xFF) : value;
    }

    uint32_t readUint32(const uint8_t* ptr, bool isBigEndian) {
        uint32_t value = *reinterpret_cast<const uint32_t*>(ptr);
        return isBigEndian ? ((value & 0xFF) << 24) | (((value >> 8) & 0xFF) << 16) | (((value >> 16) & 0xFF) << 8) | ((value >> 24) & 0xFF) : value;
    }

    int16_t readInt16(const uint8_t* ptr, bool isBigEndian) {
        return static_cast<int16_t>(readUint16(ptr, isBigEndian));
    }

    int32_t readInt32(const uint8_t* ptr, bool isBigEndian) {
        return static_cast<int32_t>(readUint32(ptr, isBigEndian));
    }
    
    float readFloat(const uint8_t* ptr, bool isBigEndian) {
        uint32_t val = readUint32(ptr, isBigEndian);
        float f;
        std::memcpy(&f, &val, sizeof(float));
        return f;
    }
}

// CRC Constants
static const uint16_t s_nCrcccittPoly = 0x1021;
static const uint16_t s_anCrcccittPolyTable[256] = {
    0x0000, 0x1021, 0x2042, 0x3063, 0x4084, 0x50a5, 0x60c6, 0x70e7,
    0x8108, 0x9129, 0xa14a, 0xb16b, 0xc18c, 0xd1ad, 0xe1ce, 0xf1ef,
    0x1231, 0x0210, 0x3273, 0x2252, 0x52b5, 0x4294, 0x72f7, 0x62d6,
    0x9339, 0x8318, 0xb37b, 0xa35a, 0xd3bd, 0xc39c, 0xf3ff, 0xe3de,
    0x2462, 0x3443, 0x0420, 0x1401, 0x64e6, 0x74c7, 0x44a4, 0x5485,
    0xa56a, 0xb54b, 0x8528, 0x9509, 0xe5ee, 0xf5cf, 0xc5ac, 0xd58d,
    0x3653, 0x2672, 0x1611, 0x0630, 0x76d7, 0x66f6, 0x5695, 0x46b4,
    0xb75b, 0xa77a, 0x9719, 0x8738, 0xf7df, 0xe7fe, 0xd79d, 0xc7bc,
    0x48c4, 0x58e5, 0x6886, 0x78a7, 0x0840, 0x1861, 0x2802, 0x3823,
    0xc9cc, 0xd9ed, 0xe98e, 0xf9af, 0x8948, 0x9969, 0xa90a, 0xb92b,
    0x5af5, 0x4ad4, 0x7ab7, 0x6a96, 0x1a71, 0x0a50, 0x3a33, 0x2a12,
    0xdbfd, 0xcbdc, 0xfbbf, 0xeb9e, 0x9b79, 0x8b58, 0xbb3b, 0xab1a,
    0x6ca6, 0x7c87, 0x4ce4, 0x5cc5, 0x2c22, 0x3c03, 0x0c60, 0x1c41,
    0xedae, 0xfd8f, 0xcdec, 0xddcd, 0xad2a, 0xbd0b, 0x8d68, 0x9d49,
    0x7e97, 0x6eb6, 0x5ed5, 0x4ef4, 0x3e13, 0x2e32, 0x1e51, 0x0e70,
    0xff9f, 0xefbe, 0xdfdd, 0xcffc, 0xbf1b, 0xaf3a, 0x9f59, 0x8f78,
    0x9188, 0x81a9, 0xb1ca, 0xa1eb, 0xd10c, 0xc12d, 0xf14e, 0xe16f,
    0x1080, 0x00a1, 0x30c2, 0x20e3, 0x5004, 0x4025, 0x7046, 0x6067,
    0x83b9, 0x9398, 0xa3fb, 0xb3da, 0xc33d, 0xd31c, 0xe37f, 0xf35e,
    0x02b1, 0x1290, 0x22f3, 0x32d2, 0x4235, 0x5214, 0x6277, 0x7256,
    0xb5ea, 0xa5cb, 0x95a8, 0x8589, 0xf56e, 0xe54f, 0xd52c, 0xc50d,
    0x34e2, 0x24c3, 0x14a0, 0x0481, 0x7466, 0x6447, 0x5424, 0x4405,
    0xa7db, 0xb7fa, 0x8799, 0x97b8, 0xe75f, 0xf77e, 0xc71d, 0xd73c,
    0x26d3, 0x36f2, 0x0691, 0x16b0, 0x6657, 0x7676, 0x4615, 0x5634,
    0xd94c, 0xc96d, 0xf90e, 0xe92f, 0x99c8, 0x89e9, 0xb98a, 0xa9ab,
    0x5844, 0x4865, 0x7806, 0x6827, 0x18c0, 0x08e1, 0x3882, 0x28a3,
    0xcb7d, 0xdb5c, 0xeb3f, 0xfb1e, 0x8bf9, 0x9bd8, 0xabbb, 0xbb9a,
    0x4a75, 0x5a54, 0x6a37, 0x7a16, 0x0af1, 0x1ad0, 0x2ab3, 0x3a92,
    0xfd2e, 0xed0f, 0xdd6c, 0xcd4d, 0xbdaa, 0xad8b, 0x9de8, 0x8dc9,
    0x7c26, 0x6c07, 0x5c64, 0x4c45, 0x3ca2, 0x2c83, 0x1ce0, 0x0cc1,
    0xef1f, 0xff3e, 0xcf5d, 0xdf7c, 0xaf9b, 0xbfba, 0x8fd9, 0x9ff8,
    0x6e17, 0x7e36, 0x4e55, 0x5e74, 0x2e93, 0x3eb2, 0x0ed1, 0x1ef0
};

const int HgbParser::TYPE_LENGTHS[8] = {0, 1, 2, 2, 4, 4, 4, 8};

HgbParser::HgbParser() {
    channelInfo_.bSelectBaso = true;
    channelInfo_.bSelectDiff = true;
    channelInfo_.bSelectNrbc = true;
    channelInfo_.bSelectRet = true;
    channelInfo_.bSelectCBC = true;
}

HgbParser::~HgbParser() {
}

HgbParseResult HgbParser::parseInfZipFile(const QString& zipFilePath) {
    if (zipFilePath.isEmpty()) return {HgbResult::OPEN_FAILED, {}, {}, {}, "ZIP文件路径为空"};
    if (!QFile::exists(zipFilePath)) return {HgbResult::OPEN_FAILED, {}, {}, {}, "ZIP文件不存在"};
    
    QByteArray unzippedData;
    if (!unzipFile(zipFilePath, unzippedData)) {
        return {HgbResult::OPEN_FAILED, {}, {}, {}, "ZIP文件解压失败"};
    }
    
    return parseInfBuffer(reinterpret_cast<const uint8_t*>(unzippedData.data()), unzippedData.size());
}

HgbParseResult HgbParser::parseInfFile(const QString& infFilePath) {
    QFile file(infFilePath);
    if (!file.open(QIODevice::ReadOnly)) return {HgbResult::OPEN_FAILED, {}, {}, {}, "无法打开INF文件"};
    QByteArray fileData = file.readAll();
    return parseInfBuffer(reinterpret_cast<const uint8_t*>(fileData.data()), fileData.size());
}

HgbParseResult HgbParser::parseInfBuffer(const uint8_t* buffer, int bufferSize) {
    HgbParseResult result;
    if (!buffer || bufferSize <= 0) return {HgbResult::DATA_ERROR, {}, {}, {}, "无效缓冲区"};
    
    result.result = readInfBuffer(buffer, bufferSize, result);
    
    if (result.result == HgbResult::SUCCESS) {
        calculateStatistics(result.hgbData);
        utils::Logger::instance().info("HGB解析完成，测量点数: " + QString::number(result.hgbData.measurementCount));
    } else {
        utils::Logger::instance().error("HGB解析失败: " + QString::fromStdString(result.errorMessage));
    }
    return result;
}

bool HgbParser::unzipFile(const QString& zipFilePath, QByteArray& unzippedData) {
    return isGzipFile(zipFilePath) ? unzipGzipFile(zipFilePath, unzippedData) : unzipZipFile(zipFilePath, unzippedData);
}

bool HgbParser::isGzipFile(const QString& filePath) {
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly)) return false;
    QByteArray header = file.read(2);
    return (header.size() >= 2 && (unsigned char)header[0] == 0x1F && (unsigned char)header[1] == 0x8B);
}

bool HgbParser::unzipGzipFile(const QString& gzipFilePath, QByteArray& unzippedData) {
    QProcess gunzip;
    gunzip.start("gunzip", QStringList() << "-c" << gzipFilePath);
    if (!gunzip.waitForStarted()) return false;
    while (!gunzip.waitForFinished(200)) unzippedData += gunzip.readAllStandardOutput();
    unzippedData += gunzip.readAllStandardOutput();
    return gunzip.exitCode() == 0 && !unzippedData.isEmpty();
}

bool HgbParser::unzipZipFile(const QString& zipFilePath, QByteArray& unzippedData) {
    QTemporaryDir tempDir;
    if (!tempDir.isValid()) return false;

    auto runUnzip = [&](bool usePwd) -> bool {
        QProcess unzip;
        unzip.setStandardInputFile(QProcess::nullDevice());
        QStringList args;
        args << "-o" << (usePwd ? "-P" : "") << (usePwd ? "password" : "") << zipFilePath << "-d" << tempDir.path();
        args.removeAll("");
        unzip.start("unzip", args);
        if (!unzip.waitForStarted()) return false;
        
        while (!unzip.waitForFinished(100)) {
            unzip.readAllStandardOutput();
        }
        return unzip.exitCode() == 0;
    };

    if (!runUnzip(true) && !runUnzip(false)) return false;

    QDir dir(tempDir.path());
    QStringList infFiles = dir.entryList(QStringList() << "*.inf" << "*.INF", QDir::Files);
    if (infFiles.isEmpty()) return false;

    QFile file(dir.absoluteFilePath(infFiles.first()));
    if (!file.open(QIODevice::ReadOnly)) return false;
    unzippedData = file.readAll();
    return !unzippedData.isEmpty();
}

// -------------------------------------------------------------------------
// CRC Methods
// -------------------------------------------------------------------------
uint16_t HgbParser::calculateCrcCcitt(const uint8_t* data, long length) {
    if (!data || length < 0) return 0;
    uint32_t crc = 0;
    const uint8_t* ptr = data;
    while (length-- != 0) {
        uint8_t da = (uint8_t)(crc >> 8);
        crc <<= 8;
        crc ^= s_anCrcccittPolyTable[da ^ *ptr];
        ptr++;
    }
    return (uint16_t)crc;
}

uint16_t HgbParser::calculateCrcCcittByBit(const uint8_t* data, long length) {
    if (!data || length < 0) return 0;
    uint32_t crc = 0;
    const uint8_t* ptr = data;
    while (length-- > 0) {
        for (uint8_t i = 0x80; i > 0; i /= 2) {
            if ((crc & 0x8000) != 0) {
                crc *= 2; crc ^= s_nCrcccittPoly;
            } else {
                crc *= 2;
            }
            if ((*ptr & i) != 0) crc ^= s_nCrcccittPoly;
        }
        ptr++;
    }
    return (uint16_t)crc;
}

HgbResult HgbParser::readInfBuffer(const uint8_t* buffer, int bufferSize, HgbParseResult& result) {
    const uint8_t* pRecord = buffer;
    const uint8_t* pEnd = buffer + bufferSize;

    if (bufferSize < 21) { result.errorMessage = "Header too small"; return HgbResult::DATA_ERROR; }
    
    // Header Info
    bool isBigEndian = (buffer[18] != 0);
    int nRecordTotal = readInt16(buffer + 19, isBigEndian);
    
    utils::Logger::instance().info(QString("Records Total: %1").arg(nRecordTotal));

    for (int i = 0; i < nRecordTotal; i++) {
        if (pRecord + 10 > pEnd) break;
        
        uint32_t storedCRC = readUint32(pRecord, isBigEndian);
        uint32_t len = readUint32(pRecord + 4, isBigEndian); 
        int16_t type = readInt16(pRecord + 8, isBigEndian);
        
        if (pRecord + 4 + len > pEnd) { 
             result.errorMessage = "Record overflow at index " + std::to_string(i); 
             return HgbResult::DATA_ERROR; 
        }

        // CRC 校验
        uint32_t calcCRC = 0xFFFF0000 | calculateCrcCcitt(pRecord + 4, len);
        if (calcCRC != storedCRC) {
            utils::Logger::instance().warning(QString("CRC Error at Rec %1 (Type %2)").arg(i).arg(type));
        }

        const uint8_t* pData = pRecord; 
        
        switch(type) {
            case 0: readHeaderInfo(pData, pEnd, result.headerInfo, isBigEndian); break;
            case 1: readVersionInfo(pData, pEnd, result.headerInfo, isBigEndian); break;
            case 2: readSampleInfo(pData, pEnd, result.headerInfo, isBigEndian); break;
            case 3: readMeasureInfo(pData, pEnd, result.headerInfo, isBigEndian); break;
            case 20: readSysFlagCode(pData, pEnd, result.headerInfo, isBigEndian); break; 
            case 21: readAlgInfo(pData, pEnd, result.calibrationCoef, isBigEndian); break; 
            case 40: {
                if (checkBoundary(pData, pEnd, 20)) {
                    // 核心逻辑: 优先尝试从 Offset 16 读取 SubType
                    int subType16 = readUint16(pData + 16, isBigEndian);
                    int subType18 = readUint16(pData + 18, isBigEndian);
                    
                    int subType = -1;
                    if (subType16 == 7 || subType16 == 17) subType = subType16;
                    else if (subType18 == 7 || subType18 == 17) subType = subType18;

                    if (subType == 7 && channelInfo_.bSelectCBC) {
                        utils::Logger::instance().info("Found HGB Measurement (Type 7)");
                        readHgbPulse(pData, pEnd, result.hgbData.measurementValues, result.hgbData.measurementCount, isBigEndian);
                    } else if (subType == 17 && channelInfo_.bSelectCBC) {
                        utils::Logger::instance().info("Found HGB Blank (Type 17)");
                        readHgbPulse(pData, pEnd, result.hgbData.blankValues, result.hgbData.blankCount, isBigEndian);
                    }
                }
                break;
            }
        }
        
        pRecord += 4 + len; 
    }
    return HgbResult::SUCCESS;
}

// -------------------------------------------------------------------------
// 缺失函数的实现 (解决 Linker Error)
// -------------------------------------------------------------------------

HgbResult HgbParser::readSysFlagCode(const uint8_t* pStart, const uint8_t* pEnd, InfHeaderInfo& headerInfo, bool isBigEndian) {
    const uint8_t* pTemp = pStart + 16;
    if (!checkBoundary(pTemp, pEnd, 4)) return HgbResult::DATA_ERROR;
    
    uint32_t dwParasTotal = readUint32(pTemp, isBigEndian);
    pTemp += 4;

    for (uint32_t i = 0; i < dwParasTotal; i++) {
        if (!checkBoundary(pTemp, pEnd, 9)) break;
        uint32_t dwParaID = readUint32(pTemp, isBigEndian); pTemp += 4;
        uint8_t ucType = *pTemp++;
        uint32_t dwElemTotal = readUint32(pTemp, isBigEndian); pTemp += 4;
        
        if (dwParaID == 2502) {
            int validLen = std::min((int)dwElemTotal, 100); 
            if (checkBoundary(pTemp, pEnd, validLen * sizeof(int))) {
                utils::Logger::instance().info(QString("Found SysFlags: ID 2502, Count=%1").arg(validLen));
            }
        }

        uint32_t dwOffset = dwElemTotal * TYPE_LENGTHS[ucType < 8 ? ucType : 0];
        pTemp += dwOffset;
    }
    return HgbResult::SUCCESS;
}

HgbResult HgbParser::readAlgInfo(const uint8_t* pStart, const uint8_t* pEnd, CalibrationCoef& coef, bool isBigEndian) {
    const uint8_t* pTemp = pStart + 16;
    if (!checkBoundary(pTemp, pEnd, 4)) return HgbResult::DATA_ERROR;
    
    uint32_t dwParasTotal = readUint32(pTemp, isBigEndian);
    pTemp += 4;

    for (uint32_t i = 0; i < dwParasTotal; i++) {
        if (!checkBoundary(pTemp, pEnd, 9)) break;
        uint32_t dwParaID = readUint32(pTemp, isBigEndian); pTemp += 4;
        uint8_t ucType = *pTemp++;
        uint32_t dwElemTotal = readUint32(pTemp, isBigEndian); pTemp += 4;
        
        if (checkBoundary(pTemp, pEnd, 4)) {
            // float fVal = readFloat(pTemp, isBigEndian);
            // if (dwParaID == 1410) { ... }
        }

        uint32_t dwOffset = dwElemTotal * TYPE_LENGTHS[ucType < 8 ? ucType : 0];
        pTemp += dwOffset;
    }
    return HgbResult::SUCCESS;
}

HgbResult HgbParser::readHgbPulse(const uint8_t* pStart, const uint8_t* pEnd,
                                 std::vector<uint16_t>& pulseData, uint32_t& dataLength, bool isBigEndian) {
    // 源码: pTemp = pStart + 18;
    const uint8_t* pTemp = pStart + 18;
    if (!checkBoundary(pTemp, pEnd, 1)) return HgbResult::DATA_ERROR;
    
    int nChnlNum = *pTemp;
    // 兼容逻辑：如果 Offset 18 不是 1，尝试偏移
    if (nChnlNum != 1) {
        pTemp += 2; 
        if (checkBoundary(pTemp, pEnd, 1)) nChnlNum = *pTemp;
    }
    
    if (nChnlNum < 0 || nChnlNum > 4) return HgbResult::DATA_ERROR; 
    pTemp++; 

    std::vector<int> vctChnlBits;
    std::vector<int> vctChnlType;
    std::vector<int> vctChnlParas;
    std::vector<int> vctParasID;
    std::vector<int> vctParasType;

    for (int i = 0; i < nChnlNum; i++) { if (!checkBoundary(pTemp, pEnd, 1)) return HgbResult::DATA_ERROR; vctChnlBits.push_back(*pTemp++); }
    for (int i = 0; i < nChnlNum; i++) { if (!checkBoundary(pTemp, pEnd, 1)) return HgbResult::DATA_ERROR; vctChnlType.push_back(*pTemp++); }
    for (int i = 0; i < nChnlNum; i++) {
        if (!checkBoundary(pTemp, pEnd, 1)) return HgbResult::DATA_ERROR;
        int nParas = *pTemp++;
        vctChnlParas.push_back(nParas);
        for(int j=0; j<nParas; j++) { if (!checkBoundary(pTemp, pEnd, 1)) return HgbResult::DATA_ERROR; vctParasID.push_back(*pTemp++); }
        for(int j=0; j<nParas; j++) { if (!checkBoundary(pTemp, pEnd, 1)) return HgbResult::DATA_ERROR; vctParasType.push_back(*pTemp++); }
    }

    if (!checkBoundary(pTemp, pEnd, 4)) return HgbResult::DATA_ERROR;
    uint32_t nCellNum = readUint32(pTemp, isBigEndian);
    pTemp += 4;

    dataLength = nCellNum;
    if (nCellNum == 0) return HgbResult::SUCCESS;

    pulseData.resize(nCellNum);
    
    int paramIdx = 0;
    for (uint32_t i = 0; i < nCellNum; i++) {
        paramIdx = 0;
        uint16_t wPeak = 0;
        int nOffset = 0;

        for (int j = 0; j < nChnlNum; j++) {
            int nParas = vctChnlParas[j];
            for (int k = 0; k < nParas; k++) {
                if (paramIdx >= vctParasType.size()) return HgbResult::DATA_ERROR;
                
                int typeIdx = vctParasType[paramIdx];
                int len = TYPE_LENGTHS[typeIdx < 8 ? typeIdx : 0]; 
                
                if (!checkBoundary(pTemp + nOffset, pEnd, len)) return HgbResult::DATA_ERROR;

                if (vctChnlType[j] == 20 && vctParasID[paramIdx] == 3) {
                    if (len == 2) wPeak = readUint16(pTemp + nOffset, isBigEndian);
                    else if (len == 4) wPeak = readUint16(pTemp + nOffset, isBigEndian); 
                }
                
                nOffset += len;
                paramIdx++;
            }
        }
        pulseData[i] = wPeak;
        pTemp += nOffset;
    }

    return HgbResult::SUCCESS;
}

HgbResult HgbParser::readMeasureInfo(const uint8_t* pStart, const uint8_t* pEnd, InfHeaderInfo& headerInfo, bool isBigEndian) {
    const uint8_t* pTemp = pStart + 16;
    if (!checkBoundary(pTemp, pEnd, 8)) return HgbResult::DATA_ERROR;
    uint32_t dwParasTotal = readUint32(pTemp, isBigEndian); pTemp += 4;
    for (uint32_t i = 0; i < dwParasTotal; i++) {
        if (!checkBoundary(pTemp, pEnd, 9)) break;
        uint32_t dwParaID = readUint32(pTemp, isBigEndian); pTemp += 4;
        uint8_t ucType = *pTemp++;
        uint32_t dwElemTotal = readUint32(pTemp, isBigEndian); pTemp += 4;
        size_t len = dwElemTotal * TYPE_LENGTHS[ucType < 8 ? ucType : 0];
        if (!checkBoundary(pTemp, pEnd, len)) break;

        switch (dwParaID) {
            case 4: { 
                 int16_t val = readInt16(pTemp, isBigEndian);
                 if (val == 1) headerInfo.measureInfo += "Canine";
                 else if (val == 2) headerInfo.measureInfo += "Feline";
                 else headerInfo.measureInfo += "Other";
                 break;
            }
            case 200: { 
                int16_t val = readInt16(pTemp, isBigEndian);
                headerInfo.hgbBlankVoltage = val;
                break;
            }
            case 201: { 
                int16_t val = readInt16(pTemp, isBigEndian);
                headerInfo.hgbMeasureVoltage = val;
                break;
            }
        }
        pTemp += len;
    }
    return HgbResult::SUCCESS;
}

HgbResult HgbParser::readHeaderInfo(const uint8_t* pStart, const uint8_t* pEnd, InfHeaderInfo& headerInfo, bool isBigEndian) {
    if (pStart + 25 > pEnd) return HgbResult::DATA_ERROR;
    headerInfo.machineType = readInt32(pStart + 21, isBigEndian);
    return HgbResult::SUCCESS;
}

void readStringParams(const uint8_t* pStart, const uint8_t* pEnd, bool isBigEndian, std::function<void(uint32_t, QString)> callback) {
    const uint8_t* pTemp = pStart + 16;
    if (pTemp + 4 > pEnd) return;
    uint32_t count = readUint32(pTemp, isBigEndian);
    if (count > 500) { pTemp += 1; if (pTemp + 4 <= pEnd) count = readUint32(pTemp, isBigEndian); }
    pTemp += 4;
    for(uint32_t i=0; i<count && pTemp < pEnd; i++) {
        if (pTemp + 10 > pEnd) break;
        uint32_t id = readUint32(pTemp, isBigEndian); pTemp += 4; pTemp++; 
        uint32_t len = readUint32(pTemp, isBigEndian); pTemp += 4;
        if (pTemp + len > pEnd) break;
        if (len > 0 && len < 256) {
            char buf[256] = {0}; memcpy(buf, pTemp, len);
            callback(id, QString::fromLatin1(buf));
        }
        pTemp += len;
    }
}

HgbResult HgbParser::readVersionInfo(const uint8_t* pStart, const uint8_t* pEnd, InfHeaderInfo& headerInfo, bool isBigEndian) {
    readStringParams(pStart, pEnd, isBigEndian, [&](uint32_t id, QString val){
        if (id == 206) headerInfo.instrumentSN = val.toStdString();
        else if (id == 201) headerInfo.version = val.toStdString();
    });
    return HgbResult::SUCCESS;
}

HgbResult HgbParser::readSampleInfo(const uint8_t* pStart, const uint8_t* pEnd, InfHeaderInfo& headerInfo, bool isBigEndian) {
    readStringParams(pStart, pEnd, isBigEndian, [&](uint32_t id, QString val){
        if (id == 1) headerInfo.sampleInfo = val.toStdString();
    });
    return HgbResult::SUCCESS;
}

HgbResult HgbParser::readCalibrationCoef(const QString& fileName, CalibrationCoef& coef) {
    coef.fHGB = 1.0f; coef.fMCV = 1.0f; coef.fPLT = 1.0f; coef.fRBC = 1.0f; coef.fWBC = 1.0f;
    return HgbResult::SUCCESS;
}

void HgbParser::calculateStatistics(HgbData& measurement) {
    if (!measurement.measurementValues.empty()) {
        const auto& values = measurement.measurementValues;
        auto minMax = std::minmax_element(values.begin(), values.end());
        measurement.minValue = *minMax.first;
        measurement.maxValue = *minMax.second;
        double sum = 0;
        for(auto v : values) sum += v;
        measurement.mean = sum / values.size();
        double sqSum = 0;
        for(auto v : values) sqSum += (v - measurement.mean) * (v - measurement.mean);
        measurement.stdDev = std::sqrt(sqSum / values.size());
    }
}

bool HgbParser::checkBoundary(const uint8_t* ptr, const uint8_t* end, size_t size) {
    return ptr && end && (ptr + size <= end);
}