#ifndef HGB_PANEL_H
#define HGB_PANEL_H

#include <QObject>
#include <QWidget>
#include <QGroupBox>
#include <QPushButton>
#include <QLineEdit>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QString>
#include <QStringList>
#include <QTextEdit>
#include <QProgressBar>
#include <QFileDialog>
#include <QFutureWatcher>
#include <QTableWidget>
#include <QHeaderView>
#include <QSplitter>
#include <QTabWidget>
#include <QtConcurrent>
#include <QMessageBox>
#include <memory>
#include <vector>
#include "modules/hgb/HgbParser.h"
#include "modules/hgb/HgbTypes.h"

// 包含头文件，但移除 using namespace，防止报错
#include <QtCharts/QChart>
#include <QtCharts/QChartView>
#include <QtCharts/QLineSeries>
#include <QtCharts/QScatterSeries>
#include <QtCharts/QValueAxis>

namespace ui {
namespace HGB {

class HGBPanel : public QWidget {
    Q_OBJECT

public:
    explicit HGBPanel(QWidget *parent = nullptr);
    ~HGBPanel();

signals:
    void dataParseCompleted(const HgbParseResult& result);
    void batchProcessStarted(int total);
    void batchProcessProgress(int current, int total);
    void batchProcessFinished();
    void batchStatisticsReady(const QString& algorithm,
                             int totalFiles,
                             int totalMeasurements,
                             double averageHgb,
                             const QMap<QString, int>& statisticsData);

public slots:
    void setCurrentFile(const QString& path);
    void setFilePaths(const QStringList& paths) { filePaths_ = paths; }
    void clearResults();

private slots:
    void onSelectFileClicked();
    void onParseFileClicked();
    void onParseBatchClicked();
    void onExportResultsClicked();
    void onClearResultsClicked();
    
    void onParseFileFinished();
    void onParseBatchFinished();

private:
    void setupUI();
    void createConnections();
    void createFileSelectionGroup();
    void createOperationGroup();
    void createResultsGroup();
    
    void createDataVisualizationGroup();
    
    void updateResultsDisplay(const HgbParseResult& result);
    void updateDataTable(const HgbParseResult& result);
    void updateStatisticsDisplay(const HgbParseResult& result);
    void updateChart(const HgbParseResult& result);
    
    void exportResultsToCSV(const QString& filePath);
    bool processBatchFiles();
    void updateBatchProgress(int current, int total);

private:
    QVBoxLayout* mainLayout_;
    QSplitter* mainSplitter_;
    
    QGroupBox* fileSelectionGroup_;
    QLineEdit* filePathEdit_;
    QPushButton* btnSelectFile_;
    
    QGroupBox* operationGroup_;
    QPushButton* btnParseFile_;
    QPushButton* btnParseBatch_;
    QPushButton* btnExportResults_;
    QPushButton* btnClearResults_;
    QProgressBar* progressBar_;
    
    QTabWidget* resultsTabWidget_;
    QTextEdit* statisticsText_;
    QTableWidget* dataTable_;
    // logText_ removed
    
    // QtCharts components (Direct usage without namespace)
    QChartView* chartView_;
    QChart* chart_;
    QLineSeries* measurementSeries_;
    QScatterSeries* blankSeries_;
    
    QString currentFilePath_;
    QStringList filePaths_;
    std::vector<HgbParseResult> batchResults_;
    HgbParseResult currentSingleResult_;
    bool hasSingleResult_ = false;
    
    QFutureWatcher<HgbParseResult>* parseWatcher_;
    QFutureWatcher<bool>* batchWatcher_;
    
    std::unique_ptr<HgbParser> parser_;
};

} // namespace HGB
} // namespace ui

#endif // HGB_PANEL_H