#include "ui/common/ToolBar.h"
#include <QHBoxLayout>
#include <QFrame>

ToolBar::ToolBar(QWidget *parent) : QWidget(parent) {
    setupUI();
    createConnections();
}

ToolBar::~ToolBar() {
}

void ToolBar::setupUI() {
    QHBoxLayout* layout = new QHBoxLayout(this);
    layout->setContentsMargins(5, 5, 5, 5);
    layout->setSpacing(5);

    // 创建基础导航按钮
    btnOpenImage_ = new QPushButton(QIcon(":/icons/open_file.svg"), "打开图像", this);
    btnOpenFolder_ = new QPushButton(QIcon(":/icons/open_folder.svg"), "打开文件夹", this);
    btnPrev_ = new QPushButton(QIcon(":/icons/prev.svg"), "上一个", this);
    btnNext_ = new QPushButton(QIcon(":/icons/next.svg"), "下一个", this);

    // 创建保存图像按钮
    btnSaveImage_ = new QPushButton(QIcon(":/icons/save_image.svg"), "保存图像", this);
    btnSaveImage_->setToolTip("保存当前图像（包含检测/标注结果）");

    // 创建更新时间戳按钮
    btnUpdateTimestamp_ = new QPushButton(QIcon(":/icons/refresh.svg"), "保存路径", this);
    btnUpdateTimestamp_->setToolTip("更新结果保存路径的时间戳");

    // 创建HGB INF文件按钮
    btnOpenInf_ = new QPushButton(QIcon(":/icons/open_file.svg"), "打开INF", this);
    btnOpenInf_->setToolTip("打开HGB INF文件并自动解析保存到Excel");

    // 添加按钮到布局
    layout->addWidget(btnOpenImage_);
    layout->addWidget(btnOpenFolder_);
    layout->addWidget(btnPrev_);
    layout->addWidget(btnNext_);

    // 添加分隔符
    QFrame* separator = new QFrame(this);
    separator->setFrameShape(QFrame::VLine);
    separator->setFrameShadow(QFrame::Sunken);
    layout->addWidget(separator);

    // 添加保存图像按钮
    layout->addWidget(btnSaveImage_);

    // 添加更新时间戳按钮
    layout->addWidget(btnUpdateTimestamp_);

    // 添加分隔符
    QFrame* separator2 = new QFrame(this);
    separator2->setFrameShape(QFrame::VLine);
    separator2->setFrameShadow(QFrame::Sunken);
    layout->addWidget(separator2);

    // 添加HGB INF文件按钮
    layout->addWidget(btnOpenInf_);

    layout->addStretch();
}

void ToolBar::createConnections() {
    // 连接基础导航按钮信号 - 使用信号转发模式
    connect(btnOpenImage_, &QPushButton::clicked, this, &ToolBar::openImageClicked);
    connect(btnOpenFolder_, &QPushButton::clicked, this, &ToolBar::openFolderClicked);
    connect(btnPrev_, &QPushButton::clicked, this, &ToolBar::prevClicked);
    connect(btnNext_, &QPushButton::clicked, this, &ToolBar::nextClicked);

    // 连接保存图像按钮信号 - 使用信号转发模式
    connect(btnSaveImage_, &QPushButton::clicked, this, &ToolBar::saveImageClicked);

    // 连接更新时间戳按钮信号 - 使用信号转发模式
    connect(btnUpdateTimestamp_, &QPushButton::clicked, this, &ToolBar::updateTimestampClicked);

    // 连接HGB INF文件按钮信号 - 使用信号转发模式
    connect(btnOpenInf_, &QPushButton::clicked, this, &ToolBar::openInfClicked);
}