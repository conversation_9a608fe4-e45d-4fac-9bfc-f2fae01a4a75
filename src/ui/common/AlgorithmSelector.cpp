#include "AlgorithmSelector.h"
#include "ui/WBC/WBCPanel.h"
#include "ui/RBC/RBCPanel.h"
#include "ui/PLT/PLTPanel.h"
#include "ui/MainWindow.h"
#include "utils/Logger.h"


AlgorithmSelector::AlgorithmSelector(MainWindow* mainWindow, QWidget *parent) : QWidget(parent),
    WBCPanel_(nullptr),
    RBCPanel_(nullptr),
    PLTPanel_(nullptr),
    mainWindow_(mainWindow) {
    setupUI();
    createConnections();
}

AlgorithmSelector::~AlgorithmSelector() {
}

void AlgorithmSelector::setupUI() {
    // 创建主布局
    QVBoxLayout* mainLayout = new QVBoxLayout(this);
    mainLayout->setContentsMargins(5, 5, 5, 5);
    mainLayout->setSpacing(5);

    // 创建算法选择区域的水平布局
    QHBoxLayout* algorithmSelectionLayout = new QHBoxLayout();
    algorithmSelectionLayout->setContentsMargins(5, 5, 5, 5);
    algorithmSelectionLayout->setSpacing(10);

    // 创建"检测算法"标签
    QLabel* algorithmLabel = new QLabel("检测算法:", this);
    algorithmLabel->setAlignment(Qt::AlignRight | Qt::AlignVCenter);

    // 创建算法选择下拉框
    algorithmComboBox_ = new QComboBox(this);
    algorithmComboBox_->addItem("WBC");
    algorithmComboBox_->addItem("RBC");
    algorithmComboBox_->addItem("PLT");
    algorithmComboBox_->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Fixed);

    // 将标签和下拉框添加到水平布局
    algorithmSelectionLayout->addWidget(algorithmLabel);
    algorithmSelectionLayout->addWidget(algorithmComboBox_);

    // 创建算法面板堆栈
    algorithmStack_ = new QStackedWidget(this);

    // 创建WBC面板
    WBCPanel_ = new ui::WBC::WBCPanel(this);
    algorithmStack_->addWidget(WBCPanel_);

    // 创建RBC面板
    RBCPanel_ = new ui::RBC::RBCPanel(this);
    algorithmStack_->addWidget(RBCPanel_);

    // 创建PLT面板
    PLTPanel_ = new ui::PLT::PLTPanel(this);
    algorithmStack_->addWidget(PLTPanel_);

    // 默认选择WBC
    algorithmComboBox_->setCurrentIndex(0);
    algorithmStack_->setCurrentIndex(0);

    // 添加组件到主布局
    mainLayout->addLayout(algorithmSelectionLayout);
    mainLayout->addWidget(algorithmStack_);
}

void AlgorithmSelector::createConnections() {
    // 连接算法选择信号
    connect(algorithmComboBox_, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &AlgorithmSelector::onAlgorithmChanged);

    // 连接WBC面板信号
    connect(WBCPanel_, &ui::WBC::WBCPanel::processedImageReady,
            this, &AlgorithmSelector::processedImageReady);
    connect(WBCPanel_, &ui::WBC::WBCPanel::validationCompleted,
            this, &AlgorithmSelector::validationCompleted);
    connect(WBCPanel_, &ui::WBC::WBCPanel::batchProcessStarted,
            this, &AlgorithmSelector::batchProcessStarted);
    connect(WBCPanel_, &ui::WBC::WBCPanel::batchProcessProgress,
            this, &AlgorithmSelector::batchProcessProgress);
    connect(WBCPanel_, &ui::WBC::WBCPanel::batchProcessFinished,
            this, &AlgorithmSelector::batchProcessFinished);
    connect(WBCPanel_, &ui::WBC::WBCPanel::detectionsReady,
            this, &AlgorithmSelector::detectionsReady);
    connect(WBCPanel_, &ui::WBC::WBCPanel::annotationsReady,
            this, &AlgorithmSelector::annotationsReady);
    connect(WBCPanel_, &ui::WBC::WBCPanel::batchStatisticsReady,
            this, &AlgorithmSelector::batchStatisticsReady);

    // 连接RBC面板信号
    connect(RBCPanel_, &ui::RBC::RBCPanel::processedImageReady,
            this, &AlgorithmSelector::processedImageReady);
    connect(RBCPanel_, &ui::RBC::RBCPanel::validationCompleted,
            this, &AlgorithmSelector::validationCompleted);
    connect(RBCPanel_, &ui::RBC::RBCPanel::batchProcessStarted,
            this, &AlgorithmSelector::batchProcessStarted);
    connect(RBCPanel_, &ui::RBC::RBCPanel::batchProcessProgress,
            this, &AlgorithmSelector::batchProcessProgress);
    connect(RBCPanel_, &ui::RBC::RBCPanel::batchProcessFinished,
            this, &AlgorithmSelector::batchProcessFinished);
    connect(RBCPanel_, &ui::RBC::RBCPanel::detectionsReady,
            this, &AlgorithmSelector::detectionsReady);
    connect(RBCPanel_, &ui::RBC::RBCPanel::annotationsReady,
            this, &AlgorithmSelector::annotationsReady);
    connect(RBCPanel_, &ui::RBC::RBCPanel::batchStatisticsReady,
            this, &AlgorithmSelector::batchStatisticsReady);

    // 连接PLT面板信号
    connect(PLTPanel_, &ui::PLT::PLTPanel::processedImageReady,
            this, &AlgorithmSelector::processedImageReady);
    connect(PLTPanel_, &ui::PLT::PLTPanel::validationCompleted,
            this, &AlgorithmSelector::validationCompleted);
    connect(PLTPanel_, &ui::PLT::PLTPanel::batchProcessStarted,
            this, &AlgorithmSelector::batchProcessStarted);
    connect(PLTPanel_, &ui::PLT::PLTPanel::batchProcessProgress,
            this, &AlgorithmSelector::batchProcessProgress);
    connect(PLTPanel_, &ui::PLT::PLTPanel::batchProcessFinished,
            this, &AlgorithmSelector::batchProcessFinished);
    connect(PLTPanel_, &ui::PLT::PLTPanel::detectionsReady,
            this, &AlgorithmSelector::detectionsReady);
    connect(PLTPanel_, &ui::PLT::PLTPanel::annotationsReady,
            this, &AlgorithmSelector::annotationsReady);
    connect(PLTPanel_, &ui::PLT::PLTPanel::batchStatisticsReady,
            this, &AlgorithmSelector::batchStatisticsReady);

void AlgorithmSelector::onAlgorithmChanged(int index) {
    // 切换到新的算法面板
    algorithmStack_->setCurrentIndex(index);

    QString algorithm;
    switch (index) {
        case 0:
            algorithm = "WBC";
            break;
        case 1:
            algorithm = "RBC";
            break;
        case 2:
            algorithm = "PLT";
            break;
        default:
            algorithm = "WBC";
            break;
    }

    emit algorithmChanged(algorithm);

    // 如果有当前图像，需要通知新的活动面板
    if (!currentImagePath_.isEmpty()) {
        setCurrentImage(currentImagePath_);
    }
}

void AlgorithmSelector::setCurrentImage(const QString& path) {
    // 保存当前图像路径
    currentImagePath_ = path;

    // 只传递给当前活动的算法面板
    QWidget* currentWidget = algorithmStack_->currentWidget();

    if (currentWidget == WBCPanel_) {
        WBCPanel_->setCurrentImage(path);
    } else if (currentWidget == RBCPanel_) {
        RBCPanel_->setCurrentImage(path);
    } else if (currentWidget == PLTPanel_) {
        PLTPanel_->setCurrentImage(path);
}

void AlgorithmSelector::setImagePaths(const QStringList& paths) {
    // 传递给所有算法面板
    WBCPanel_->setImagePaths(paths);
    RBCPanel_->setImagePaths(paths);
    PLTPanel_->setImagePaths(paths);
}