#ifndef ALGORITHM_SELECTOR_H
#define ALGORITHM_SELECTOR_H

#include <QWidget>
#include <QStackedWidget>
#include <QComboBox>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QString>
#include <QStringList>
#include <QSizePolicy>
#include <opencv2/core/core.hpp>
#include "modules/detection/YoloAlgorithm.h"

// 前向声明
class MainWindow;

namespace ui {
namespace WBC {
class WBCPanel;
}
namespace RBC {
class RBCPanel;
}
namespace PLT {
class PLTPanel;
}
}

// 使用命名空间别名简化代码
using ui::WBC::WBCPanel;
using ui::RBC::RBCPanel;
using ui::PLT::PLTPanel;

class AlgorithmSelector : public QWidget {
    Q_OBJECT
public:
    explicit AlgorithmSelector(MainWindow* mainWindow, QWidget *parent = nullptr);
    ~AlgorithmSelector();

signals:
    void algorithmChanged(const QString& algorithm);
    void processedImageReady(const cv::Mat& result);
    void validationCompleted(const EvaluationMetrics& metrics);

    // 检测结果相关信号
    void detectionsReady(const QList<Detection>& detections);

    // 标注结果相关信号
    void annotationsReady(const QList<Detection>& annotations);

    // 进度报告相关信号
    void batchProcessStarted(int total);
    void batchProcessProgress(int current, int total);
    void batchProcessFinished();

    // 批量预测结果统计信号
    void batchStatisticsReady(const QString& algorithm,
                             int totalImages,
                             int totalDetections,
                             double averagePerImage,
                             const QMap<QString, int>& labelCounts);

public slots:
    void setCurrentImage(const QString& path);
    void setImagePaths(const QStringList& paths);

private slots:
    void onAlgorithmChanged(int index);

private:
    void setupUI();
    void createConnections();

    // UI组件
    QComboBox* algorithmComboBox_;
    QStackedWidget* algorithmStack_;

    // 算法面板
    ui::WBC::WBCPanel* WBCPanel_;
    ui::RBC::RBCPanel* RBCPanel_;
    ui::PLT::PLTPanel* PLTPanel_;

    // MainWindow 引用
    MainWindow* mainWindow_;

    // 当前图像路径
    QString currentImagePath_;
};

#endif // ALGORITHM_SELECTOR_H