#ifndef MAIN_WINDOW_H
#define MAIN_WINDOW_H

#include <QMainWindow>
#include <QString>
#include <QStringList>
#include <QFile>
#include <QFileDialog>
#include <QDir>
#include <QMessageBox>
#include <QStyleFactory>
#include <QStatusBar>
#include <QProgressBar>
#include <QFont>
#include <QApplication>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QTabWidget>
#include <opencv2/core/core.hpp>

#include "utils/ImageDrawer.h"
#include "ui/common/ToolBar.h"
#include "ui/common/ImageView.h"
#include "ui/common/FileList.h"
#include "ui/common/LogView.h"
#include "ui/common/AlgorithmSelector.h"
#include "ui/common/AllChannelResultsPanel.h"
#include "ui/common/PerformanceResultsWidget.h"
#include "modules/hgb/HgbParser.h"

// 前向声明
class FileList;
class ImageView;
class LogView;
class ToolBar;
class AlgorithmSelector;
class AllChannelResultsPanel;

namespace ui {
namespace common {
class PerformanceResultsWidget;
}
}

class MainWindow : public QMainWindow {
    Q_OBJECT
public:
    explicit MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

signals:
    void currentImageChanged(const QString& path);
    void imagePathsChanged(const QStringList& paths);

private slots:
    // 基础导航相关槽
    void on_openImageClicked();
    void on_openFolderClicked();
    void on_prevClicked();
    void on_nextClicked();
    void on_currentFileChanged(const QString& file);

    // 批处理相关槽
    void onBatchProcessStarted(int total);
    void onBatchProcessProgress(int current, int total);
    void onBatchProcessFinished();

    // 保存图像相关槽
    void on_saveImageClicked();

    // 更新时间戳相关槽
    void on_updateTimestampClicked();

    // HGB INF文件相关槽
    void on_openInfClicked();

private:
    void setupUI();
    void createConnections();
    void showCurrentImage();
    bool saveHgbResultToCSV(const HgbParseResult& result, const QString& filePath, const QString& sourceFile);

    // UI组件
    FileList* fileList_;
    ImageView* imageView_;
    LogView* logView_;
    ToolBar* toolBar_;
    AlgorithmSelector* algorithmSelector_;
    AllChannelResultsPanel* allChannelResultsPanel_;
    ui::common::PerformanceResultsWidget* performanceWidget_;
    QTabWidget* rightTabWidget_; // 右侧标签页容器

    // 状态栏
    QStatusBar* statusBar_;

    // 图像数据
    QString imagePath_;
    QStringList fullPathList_;
    int currentImageIndex_ = -1;
};

#endif // MAIN_WINDOW_H